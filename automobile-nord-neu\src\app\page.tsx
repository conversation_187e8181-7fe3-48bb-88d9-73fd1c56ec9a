export default function Home() {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section - GT Performance Cars Style */}
      <section className="relative py-24 lg:py-32 bg-white">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24">
          <div className="text-center">
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-light text-gray-900 mb-12 leading-tight">
              Finde dein <span className="italic">Traumauto</span> bei<br />
              Automobile Nord GmbH
            </h1>

            <div className="flex justify-center items-center gap-4 mb-16">
              <a href="/fahrzeuge" className="text-lg text-gray-600 hover:text-black transition-colors">
                Fahrzeuge im Lager
              </a>
              <div className="bg-black text-white px-4 py-2 rounded-full">
                <span className="text-xl font-bold">62</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Brand Logos - Clean Grid */}
      <section className="py-16 bg-white">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
            {[
              { name: 'Audi', link: '/fahrzeuge?marke=audi' },
              { name: 'BMW', link: '/fahrzeuge?marke=bmw' },
              { name: 'Mercedes-Benz', link: '/fahrzeuge?marke=mercedes' },
              { name: 'Porsche', link: '/fahrzeuge?marke=porsche' },
              { name: 'Volkswagen', link: '/fahrzeuge?marke=volkswagen' },
              { name: 'SKODA', link: '/fahrzeuge?marke=skoda' },
              { name: 'Tesla', link: '/fahrzeuge?marke=tesla' },
              { name: 'Ford', link: '/fahrzeuge?marke=ford' }
            ].map((brand) => (
              <a key={brand.name} href={brand.link} className="block text-center group">
                <div className="border border-gray-200 p-6 rounded-lg hover:border-gray-400 transition-colors">
                  <h3 className="font-medium text-gray-800 group-hover:text-black transition-colors">{brand.name}</h3>
                </div>
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* About Section - GT Style */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-light text-gray-900 mb-8">
              Große Begeisterung für Autos mit hoher Leistung
            </h2>
            <p className="text-xl text-gray-600 mb-6 leading-relaxed">
              <strong>Gegründet 2019 und ansässig in Flensburg mit lieferbereiten Premiumfahrzeugen.</strong>
            </p>
            <p className="text-lg text-gray-600 mb-12 leading-relaxed">
              Dank unserer konstanten Bestrebung, die Anforderungen unserer Kunden zu erfüllen, gelten wir heute als einer der größten Premiumhändler in Norddeutschland.
            </p>
            <a href="/ueber-uns" className="inline-block border border-gray-900 text-gray-900 px-8 py-3 hover:bg-gray-900 hover:text-white transition-colors">
              Über uns
            </a>
          </div>
        </div>
      </section>

      {/* Featured Vehicles - Real Data */}
      <section className="py-20 bg-white">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-light text-gray-900 mb-6">
              <span className="italic">Premiumfahrzeuge</span> Bereit für Lieferung
            </h2>
            <a href="/fahrzeuge" className="text-black underline hover:no-underline text-lg">
              Alle anzeigen
            </a>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Audi R8 Spyder */}
            <div className="bg-white border border-gray-200 overflow-hidden hover:border-gray-400 transition-colors group">
              <div className="aspect-[4/3] bg-gray-100 relative overflow-hidden">
                <img
                  src="https://img.classistatic.de/api/v1/mo-prod/images/01/01292fa3-86c7-4894-a5b5-e5175f74d8a8?rule=mo-640.jpg"
                  alt="Audi R8 Spyder"
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-1">Audi R8</h3>
                <p className="text-gray-600 mb-4">Spyder 5.2 FSI Quattro Performance Carbon</p>
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-light text-gray-900">€ 216.000</span>
                  <a href="/fahrzeug/audi-r8-spyder" className="text-black underline hover:no-underline">
                    Details
                  </a>
                </div>
              </div>
            </div>

            {/* Mercedes GLE 63 S AMG */}
            <div className="bg-white border border-gray-200 overflow-hidden hover:border-gray-400 transition-colors group">
              <div className="aspect-[4/3] bg-gray-100 relative overflow-hidden">
                <img
                  src="https://img.classistatic.de/api/v1/mo-prod/images/14/149a897a-22b0-4770-8905-4fb1d8c64726?rule=mo-640.jpg"
                  alt="Mercedes-Benz GLE 63 S AMG"
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-1">Mercedes-Benz GLE 63 S AMG</h3>
                <p className="text-gray-600 mb-4">4Matic+ Coupe Brabus 700</p>
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-light text-gray-900">€ 176.000</span>
                  <a href="/fahrzeug/mercedes-gle-63-amg" className="text-black underline hover:no-underline">
                    Details
                  </a>
                </div>
              </div>
            </div>

            {/* Porsche Cayenne */}
            <div className="bg-white border border-gray-200 overflow-hidden hover:border-gray-400 transition-colors group">
              <div className="aspect-[4/3] bg-gray-100 relative overflow-hidden">
                <img
                  src="https://img.classistatic.de/api/v1/mo-prod/images/63/6339fcba-efe9-4ebf-aa3d-2ce424302fc2?rule=mo-640.jpg"
                  alt="Porsche Cayenne Coupe GTS"
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-1">Porsche Cayenne</h3>
                <p className="text-gray-600 mb-4">Coupe GTS*Pano*Head Up*Soft Close</p>
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-light text-gray-900">€ 139.500</span>
                  <a href="/fahrzeug/porsche-cayenne-gts" className="text-black underline hover:no-underline">
                    Details
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sell Your Car - GT Style */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-light text-gray-900 mb-6">
                Verkaufe dein Auto an uns
              </h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                Bewertung innerhalb von 24 Stunden. Kostenlose Abholung in ganz Deutschland. Sicher, Einfach & Bequem
              </p>
              <a href="/verkaufen" className="inline-block border border-gray-900 text-gray-900 px-8 py-3 hover:bg-gray-900 hover:text-white transition-colors">
                Verkaufe dein Auto
              </a>
            </div>
            <div className="bg-white p-8 border border-gray-200">
              <form className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm text-gray-600 mb-2">Service</label>
                    <select className="w-full p-3 border border-gray-300 focus:border-gray-900 focus:outline-none">
                      <option>Verkaufe dein Auto</option>
                      <option>Vermittle dein Auto</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-2">Kennzeichen</label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-300 focus:border-gray-900 focus:outline-none"
                      placeholder="FL-AB 123"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm text-gray-600 mb-2">Kilometerstand</label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-300 focus:border-gray-900 focus:outline-none"
                      placeholder="50.000 km"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-2">Fahrzeugmodell</label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-300 focus:border-gray-900 focus:outline-none"
                      placeholder="BMW X5"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm text-gray-600 mb-2">E-Mail</label>
                    <input
                      type="email"
                      className="w-full p-3 border border-gray-300 focus:border-gray-900 focus:outline-none"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-2">Telefon</label>
                    <input
                      type="tel"
                      className="w-full p-3 border border-gray-300 focus:border-gray-900 focus:outline-none"
                      placeholder="0461-66353453"
                    />
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full bg-black text-white py-3 hover:bg-gray-800 transition-colors"
                >
                  Bewertung erhalten
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Services - GT Style */}
      <section className="py-20 bg-white">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">✓</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Durchgehende Kontrollen</h3>
              <p className="text-gray-600 text-sm">
                Sämtliche Fahrzeuge durchlaufen eine extensive Kontrolle für höchste Qualitätsstandards.
              </p>
              <a href="/fahrzeuge" className="inline-block mt-4 text-black underline hover:no-underline text-sm">
                Fahrzeuge im Lager
              </a>
            </div>
            <div className="text-center">
              <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚗</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Lieferfertige Premiumfahrzeuge</h3>
              <p className="text-gray-600 text-sm">
                Wir haben eine große Begeisterung für Fahrzeuge mit hoher Leistung in der Premiumklasse.
              </p>
              <a href="/fahrzeuge" className="inline-block mt-4 text-black underline hover:no-underline text-sm">
                Fahrzeuge im Lager
              </a>
            </div>
            <div className="text-center">
              <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🛡️</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Garantien</h3>
              <p className="text-gray-600 text-sm">
                Bei uns können Sie immer bis zu 3 Jahre Garantie dazukaufen. Sprechen Sie mit unserem Team.
              </p>
              <a href="/garantie" className="inline-block mt-4 text-black underline hover:no-underline text-sm">
                Garantien
              </a>
            </div>
            <div className="text-center">
              <div className="bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Bequeme Geschäfte</h3>
              <p className="text-gray-600 text-sm">
                Unsere Pflicht ist, dass Sie sich vom ersten Kontakt an wohlfühlen und zufrieden sind.
              </p>
              <a href="/kontakt" className="inline-block mt-4 text-black underline hover:no-underline text-sm">
                Kontakt
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Reviews - GT Style */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24 text-center">
          <h2 className="text-3xl font-light text-gray-900 mb-6 max-w-4xl mx-auto leading-relaxed">
            Bei Automobile Nord schätzen wir Kundenzufriedenheit hoch und streben danach, unseren Kunden die bestmögliche Kauferfahrung zu bieten
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Lesen Sie über 500 Bewertungen, um zu sehen, was unsere bisherigen Kunden über uns sagen!
          </p>
          <a href="/bewertungen" className="inline-block border border-gray-900 text-gray-900 px-8 py-3 hover:bg-gray-900 hover:text-white transition-colors">
            Bewertungen lesen
          </a>
        </div>
      </section>
    </main>
  );
}
