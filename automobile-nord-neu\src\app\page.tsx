'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, Car, Shield, Star, Users, CheckCircle, Phone, Search, Filter, Calendar, Fuel, Settings, ShoppingCart, DollarSign, Headphones, Award, Clock, MapPin } from 'lucide-react';

export default function Home() {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight max-w-4xl mx-auto">
                <PERSON><PERSON> dein{' '}
                <span className="italic font-light">Traumauto</span>{' '}
                bei Automobile Nord GmbH
              </h1>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mt-12"
            >
              <Button 
                size="lg" 
                className="bg-black text-white hover:bg-gray-800 px-8 py-4 text-lg font-medium rounded-md"
                asChild
              >
                <Link href="/fahrzeuge">
                  Fahrzeuge im Lager
                  <span className="ml-2 bg-white text-black px-2 py-1 rounded text-sm font-bold">
                    50
                  </span>
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Marken Grid Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { name: 'Audi', href: '/fahrzeuge?marke=audi' },
              { name: 'BMW', href: '/fahrzeuge?marke=bmw' },
              { name: 'Mercedes-Benz', href: '/fahrzeuge?marke=mercedes' },
              { name: 'Porsche', href: '/fahrzeuge?marke=porsche' },
              { name: 'Volkswagen', href: '/fahrzeuge?marke=volkswagen' },
              { name: 'Ford', href: '/fahrzeuge?marke=ford' },
              { name: 'Opel', href: '/fahrzeuge?marke=opel' },
              { name: 'Tesla', href: '/fahrzeuge?marke=tesla' },
            ].map((brand) => (
              <motion.div
                key={brand.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Link
                  href={brand.href}
                  className="block p-6 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200 text-center"
                >
                  <h3 className="text-lg font-semibold text-gray-900">{brand.name}</h3>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Über uns Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Große Begeisterung für Autos mit hoher Leistung
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                <strong>Gegründet 2010 und ansässig in Flensburg mit lieferbereiten Premiumfahrzeugen.</strong>
              </p>
              <p className="text-gray-600 mb-8">
                Dank eines konstanten Strebens nach Erfüllung der Anforderungen unserer Kunden gelten wir heute als einer der größten Premiumhändler in Norddeutschland.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center lg:text-right"
            >
              <Button 
                size="lg" 
                className="bg-black text-white hover:bg-gray-800 px-8 py-4 text-lg font-medium rounded-md"
                asChild
              >
                <Link href="/ueber-uns">
                  Über uns
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Fahrzeuge Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              <span className="italic font-light">Premiumfahrzeuge</span> Bereit zur Lieferung
            </h2>
            <Button 
              variant="outline" 
              size="lg" 
              className="border-black text-black hover:bg-black hover:text-white"
              asChild
            >
              <Link href="/fahrzeuge">
                Alle anzeigen
              </Link>
            </Button>
          </div>

          {/* Fahrzeug Karten würden hier kommen */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Placeholder für Fahrzeug-Karten */}
            {[1, 2, 3].map((i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: i * 0.1 }}
                viewport={{ once: true }}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"
              >
                <div className="h-48 bg-gray-200"></div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Fahrzeug {i}</h3>
                  <p className="text-gray-600 mb-4">Beschreibung des Fahrzeugs...</p>
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold text-gray-900">€ 45.000</span>
                    <Button size="sm" variant="outline">
                      Details
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Verkaufen Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Verkaufe dein Auto an uns
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Bewertung innerhalb von 24 Stunden. Kostenlose Abholung in ganz Deutschland. Sicher, Einfach & Reibungslos
              </p>
              <Button 
                size="lg" 
                className="bg-black text-white hover:bg-gray-800 px-8 py-4 text-lg font-medium rounded-md"
                asChild
              >
                <Link href="/verkaufen">
                  Verkaufe dein Auto
                </Link>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-8 rounded-lg border border-gray-200"
            >
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Kennzeichen</label>
                  <input 
                    type="text" 
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-black"
                    placeholder="z.B. FL-AB 123"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Kilometerstand</label>
                  <input 
                    type="text" 
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-black"
                    placeholder="z.B. 50.000 km"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">E-Mail</label>
                  <input 
                    type="email" 
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-black"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Telefon</label>
                  <input 
                    type="tel" 
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-black"
                    placeholder="0461-66353453"
                  />
                </div>
                <Button 
                  type="submit" 
                  className="w-full bg-black text-white hover:bg-gray-800 py-3"
                >
                  Bewertung erhalten
                </Button>
              </form>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Service Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Geprüfte Qualität */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Durchgehende Kontrollen</h3>
              <p className="text-gray-600">
                Samtliche Fahrzeuge durchlaufen eine extensive Kontrolle für höchste Qualitätsstandards.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4 border-black text-black hover:bg-black hover:text-white"
                asChild
              >
                <Link href="/fahrzeuge">
                  Fahrzeuge im Lager
                </Link>
              </Button>
            </motion.div>

            {/* Lieferbereit */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Car className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Lieferbereit Premiumfahrzeuge</h3>
              <p className="text-gray-600">
                Wir haben eine große Begeisterung für Autos in hoher Leistung in der Premiumklasse.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4 border-black text-black hover:bg-black hover:text-white"
                asChild
              >
                <Link href="/fahrzeuge">
                  Fahrzeuge im Lager
                </Link>
              </Button>
            </motion.div>

            {/* Garantien */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Garantien</h3>
              <p className="text-gray-600">
                Bei uns können Sie immer bis zu 3 Jahre Garantie dazukaufen. Sprechen Sie mit unseren Verkäufern.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4 border-black text-black hover:bg-black hover:text-white"
                asChild
              >
                <Link href="/garantie">
                  Garantien
                </Link>
              </Button>
            </motion.div>

            {/* Bequeme Geschäfte */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Bequeme Geschäfte</h3>
              <p className="text-gray-600">
                Unsere Pflicht ist, dass Sie sich vom ersten Kontakt an wohl fühlen und zufrieden sind.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4 border-black text-black hover:bg-black hover:text-white"
                asChild
              >
                <Link href="/kontakt">
                  Kontakt
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Kundenbewertungen Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Bei Automobile Nord schätzen wir Kundenzufriedenheit hoch und streben danach, unseren Kunden die bestmögliche Kauferfahrung zu bieten
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Lesen Sie über 500 Bewertungen, um zu sehen, was unsere bisherigen Kunden über uns sagen!
            </p>
            <Button
              size="lg"
              className="bg-black text-white hover:bg-gray-800 px-8 py-4 text-lg font-medium rounded-md"
              asChild
            >
              <Link href="/bewertungen">
                Bewertungen lesen
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>
    </main>
  );
}
