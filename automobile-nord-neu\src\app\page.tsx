'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, Car, Shield, Star, Users, CheckCircle, Phone, Search, Filter, Calendar, Fuel, Settings, ShoppingCart, DollarSign, Headphones, Award, Clock, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70 z-10"></div>
          <img
            src="https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2083&q=80"
            alt="Luxury Cars"
            className="w-full h-full object-cover"
          />
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Finde dein{' '}
              <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
                Traumauto
              </span>
            </h1>
            <p className="text-lg sm:text-xl lg:text-2xl text-gray-200 mb-12 max-w-3xl mx-auto leading-relaxed">
              Automobile Nord GmbH - Ihr zuverlässiger Partner für hochwertige Fahrzeuge in Flensburg und Umgebung.
            </p>
          </motion.div>

          {/* Search Form */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
            className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 lg:p-8 max-w-5xl mx-auto mb-12"
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Marke</label>
                <select className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white">
                  <option>Alle Marken</option>
                  <option>Mercedes</option>
                  <option>BMW</option>
                  <option>Audi</option>
                  <option>Volkswagen</option>
                  <option>Porsche</option>
                  <option>Ford</option>
                  <option>Opel</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Modell</label>
                <select className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white">
                  <option>Alle Modelle</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Karosserie</label>
                <select className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white">
                  <option>Alle Typen</option>
                  <option>Limousine</option>
                  <option>Kombi</option>
                  <option>SUV</option>
                  <option>Cabrio</option>
                  <option>Coupé</option>
                  <option>Kleinwagen</option>
                </select>
              </div>
              <div className="flex items-end">
                <Button size="xl" className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200" asChild>
                  <Link href="/fahrzeuge">
                    <Search className="mr-2 h-5 w-5" />
                    Suchen
                  </Link>
                </Button>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center"
          >
            <Button size="xl" variant="outline" className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm font-semibold" asChild>
              <Link href="/fahrzeuge">Alle Fahrzeuge ansehen</Link>
            </Button>
            <Button size="xl" variant="outline" className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm font-semibold" asChild>
              <Link href="/kontakt">
                <Phone className="mr-2 h-5 w-5" />
                0461-66353453
              </Link>
            </Button>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        >
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce"></div>
          </div>
        </motion.div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Unsere <span className="bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">Dienstleistungen</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Wir bieten Ihnen umfassende Services rund um Ihr Fahrzeug - von der Beratung bis zum Verkauf.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            {/* Fahrzeugverkauf */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <ShoppingCart className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors">Fahrzeugverkauf</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Große Auswahl an hochwertigen Gebrauchtwagen aller Marken und Modelle.
                </p>
                <ul className="text-gray-600 space-y-3 mb-8">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Geprüfte Fahrzeugqualität
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Faire Preise
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Umfassende Beratung
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Garantieleistungen
                  </li>
                </ul>
                <Button variant="outline" className="w-full border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-semibold group-hover:shadow-lg transition-all duration-300">
                  Mehr erfahren
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </motion.div>

            {/* Fahrzeugankauf */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors">Fahrzeugankauf</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Wir kaufen Ihr Fahrzeug zu fairen Marktpreisen - schnell und unkompliziert.
                </p>
                <ul className="text-gray-600 space-y-3 mb-8">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Kostenlose Bewertung
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Sofortige Barauszahlung
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Alle Marken und Modelle
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Professionelle Abwicklung
                  </li>
                </ul>
                <Button variant="outline" className="w-full border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-semibold group-hover:shadow-lg transition-all duration-300">
                  Mehr erfahren
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </motion.div>

            {/* Beratung & Service */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Headphones className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors">Beratung & Service</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Persönliche Beratung und umfassender Service für alle Ihre Fahrzeugbedürfnisse.
                </p>
                <ul className="text-gray-600 space-y-3 mb-8">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Individuelle Beratung
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Finanzierungshilfe
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Zulassungsservice
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    Nachbetreuung
                  </li>
                </ul>
                <Button variant="outline" className="w-full border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-semibold group-hover:shadow-lg transition-all duration-300">
                  Mehr erfahren
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Über uns / Willkommen Section */}
      <section className="relative py-20 bg-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Herzlich <span className="bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">Willkommen</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Kundenzufriedenheit hat für uns höchste Priorität.
              </p>
              <p className="text-xl lg:text-2xl font-semibold text-gray-800 mt-4">
                Nicht nur dies, sondern <span className="text-orange-500">WIR wollen DICH begeistern!</span>
              </p>
            </motion.div>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16">
            {/* Left Side - Features */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div className="grid sm:grid-cols-2 gap-6">
                <div className="group">
                  <div className="flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <CheckCircle className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-gray-900">Präzise Antworten</p>
                      <p className="text-sm text-gray-600">Ehrliche Beratung</p>
                    </div>
                  </div>
                </div>

                <div className="group">
                  <div className="flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-gray-900">Wie zu Hause</p>
                      <p className="text-sm text-gray-600">Familiäre Atmosphäre</p>
                    </div>
                  </div>
                </div>

                <div className="group">
                  <div className="flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Star className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-gray-900">Alle Freiheiten</p>
                      <p className="text-sm text-gray-600">Flexible Lösungen</p>
                    </div>
                  </div>
                </div>

                <div className="group">
                  <div className="flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Car className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-gray-900">Immer mobil</p>
                      <p className="text-sm text-gray-600">Zuverlässiger Service</p>
                    </div>
                  </div>
                </div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-center lg:text-left"
              >
                <p className="text-2xl font-bold text-gray-900 mb-4">
                  Bei uns erwirbst du <span className="text-orange-500">„das Auto"</span>
                </p>
                <p className="text-gray-600 leading-relaxed">
                  Seit über 15 Jahren stehen wir für Qualität, Vertrauen und erstklassigen Service.
                  Unser erfahrenes Team begleitet Sie von der ersten Beratung bis zur Schlüsselübergabe.
                </p>
              </motion.div>
            </motion.div>

            {/* Right Side - Contact Card */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-3xl p-8 lg:p-12 text-white shadow-2xl">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full translate-y-12 -translate-x-12"></div>

                <div className="relative z-10">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center">
                      <Phone className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold">Hast Du Fragen?</h3>
                      <p className="text-gray-300">Wir sind für dich da!</p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <p className="text-gray-300 mb-2">Ruf uns direkt an:</p>
                      <a
                        href="tel:+4946166353453"
                        className="text-3xl lg:text-4xl font-bold text-white hover:text-orange-400 transition-colors duration-300 block"
                      >
                        0461-66353453
                      </a>
                    </div>

                    <div className="border-t border-gray-700 pt-6">
                      <div className="flex items-center space-x-3 text-gray-300">
                        <Clock className="h-5 w-5 text-orange-400" />
                        <div>
                          <p className="font-semibold text-white">Öffnungszeiten:</p>
                          <p className="text-sm">Mo-Fr: 10:00 - 18:00</p>
                          <p className="text-sm">Sa: 10:00 - 13:00</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 text-gray-300">
                      <MapPin className="h-5 w-5 text-orange-400" />
                      <div>
                        <p className="font-semibold text-white">Besuche uns:</p>
                        <p className="text-sm">Grönfahrtweg 22, 24955 Harrislee</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="clean-card p-6 text-center"
            >
              <Car className="h-12 w-12 primary-blue mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-4">Wir kaufen Jedes Auto</h3>
              <p className="text-gray-600 mb-4">
                Sie wollen Ihr Auto verkaufen? Das trifft sich gut. In uns haben Sie einen zuverlässigen Partner.
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Eine marktgerechte Schätzung Ihres Fahrzeugs</li>
                <li>• Finanzkräftige Vormerkkunden</li>
                <li>• Individuelle und persönliche Betreuung</li>
                <li>• Kraftfahrzeuge jeglicher Art</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="clean-card p-6 text-center"
            >
              <Settings className="h-12 w-12 primary-blue mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-4">Dienstleistungen</h3>
              <p className="text-gray-600 mb-4">
                Gebrauchtwagen An- und Verkauf mit umfassendem Service.
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Große Auswahl an Fahrzeugen</li>
                <li>• Wir besorgen Ihr Wunschfahrzeug</li>
                <li>• Inzahlungnahme</li>
                <li>• Sofortankauf gegen Bargeld</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="clean-card p-6 text-center"
            >
              <Users className="h-12 w-12 primary-blue mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-4">Unser Team</h3>
              <p className="text-gray-600 mb-4">
                Unser Team steht Ihnen während der Öffnungszeiten zur Verfügung.
              </p>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>Öffnungszeiten:</strong></p>
                <p>Mo-Fr: 10:00 - 18:00</p>
                <p>Sa: 10:00 - 13:00</p>
                <p>So: Geschlossen</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="clean-section py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              Bereit für Ihr neues Auto?
            </h2>
            <p className="text-xl text-gray-600">
              Kontaktieren Sie uns noch heute und finden Sie Ihr Traumfahrzeug.
              Unser Team berät Sie gerne persönlich.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary-blue hover:bg-primary-blue text-white px-8 py-3" asChild>
                <Link href="/kontakt">
                  <Phone className="mr-2 h-5 w-5" />
                  Jetzt Kontakt aufnehmen
                </Link>
              </Button>

              <Button size="lg" variant="outline" className="border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white px-8 py-3" asChild>
                <Link href="/fahrzeuge">
                  <Car className="mr-2 h-5 w-5" />
                  Fahrzeuge ansehen
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full -translate-x-48 -translate-y-48"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full translate-x-48 translate-y-48"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Zahlen, die für sich <span className="bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">sprechen</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Über 15 Jahre Erfahrung und tausende zufriedene Kunden sprechen für unsere Qualität und Zuverlässigkeit.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Fahrzeuge im Bestand */}
            <motion.div
              initial={{ opacity: 0, y: 50, scale: 0.8 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="group text-center"
            >
              <div className="relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Car className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300">
                    5k+
                  </div>
                  <div className="text-gray-300 font-semibold text-lg">Fahrzeuge</div>
                  <div className="text-gray-400 text-sm">im Bestand</div>
                </div>
              </div>
            </motion.div>

            {/* Probefahrten */}
            <motion.div
              initial={{ opacity: 0, y: 50, scale: 0.8 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="group text-center"
            >
              <div className="relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Calendar className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300">
                    680+
                  </div>
                  <div className="text-gray-300 font-semibold text-lg">Probefahrten</div>
                  <div className="text-gray-400 text-sm">durchgeführt</div>
                </div>
              </div>
            </motion.div>

            {/* Jahre Erfahrung */}
            <motion.div
              initial={{ opacity: 0, y: 50, scale: 0.8 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="group text-center"
            >
              <div className="relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Award className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300">
                    15+
                  </div>
                  <div className="text-gray-300 font-semibold text-lg">Jahre</div>
                  <div className="text-gray-400 text-sm">Erfahrung</div>
                </div>
              </div>
            </motion.div>

            {/* Kundenzufriedenheit */}
            <motion.div
              initial={{ opacity: 0, y: 50, scale: 0.8 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="group text-center"
            >
              <div className="relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Star className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300">
                    100%
                  </div>
                  <div className="text-gray-300 font-semibold text-lg">Kunden-</div>
                  <div className="text-gray-400 text-sm">zufriedenheit</div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-16 text-center"
          >
            <p className="text-gray-300 text-lg mb-8">
              Vertrauen Sie auf unsere Expertise und werden Sie Teil unserer zufriedenen Kundenfamilie
            </p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              <div className="flex items-center space-x-2">
                <Shield className="h-6 w-6 text-green-400" />
                <span className="text-gray-300">Geprüfte Qualität</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-6 w-6 text-green-400" />
                <span className="text-gray-300">Faire Preise</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-green-400" />
                <span className="text-gray-300">Persönlicher Service</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="bg-orange-500 text-white section-padding">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-4xl lg:text-5xl font-bold">
              Ihr Traumauto ist nur einen Besuch entfernt!
            </h2>
            <p className="text-xl opacity-90">
              Besuchen Sie uns noch heute für nur <strong>149€/Monat</strong>
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-orange-500 hover:bg-gray-100 px-8 py-3" asChild>
                <Link href="/kontakt">
                  Besuchen Sie uns heute
                </Link>
              </Button>

              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-500 px-8 py-3" asChild>
                <Link href="/fahrzeuge">
                  <Phone className="mr-2 h-5 w-5" />
                  0461-66353453
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
