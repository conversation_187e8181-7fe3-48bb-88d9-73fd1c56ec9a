{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ArrowRight, Car, Shield, Star, Users, CheckCircle, Phone, Search, Filter, Calendar, Fuel, Settings, ShoppingCart, DollarSign, Headphones, Award, Clock, MapPin } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n        {/* Background Image */}\n        <div className=\"absolute inset-0 z-0\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70 z-10\"></div>\n          <img\n            src=\"https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2083&q=80\"\n            alt=\"Luxury Cars\"\n            className=\"w-full h-full object-cover\"\n          />\n        </div>\n\n        <div className=\"relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, ease: \"easeOut\" }}\n          >\n            <h1 className=\"text-4xl sm:text-5xl lg:text-7xl font-bold text-white mb-6 leading-tight\">\n              Finde dein{' '}\n              <span className=\"bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent\">\n                Traumauto\n              </span>\n            </h1>\n            <p className=\"text-lg sm:text-xl lg:text-2xl text-gray-200 mb-12 max-w-3xl mx-auto leading-relaxed\">\n              Automobile Nord GmbH - Ihr zuverlässiger Partner für hochwertige Fahrzeuge in Flensburg und Umgebung.\n            </p>\n          </motion.div>\n\n          {/* Search Form */}\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.3, ease: \"easeOut\" }}\n            className=\"bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 lg:p-8 max-w-5xl mx-auto mb-12\"\n          >\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6\">\n              <div>\n                <label className=\"block text-sm font-semibold text-gray-700 mb-3\">Marke</label>\n                <select className=\"w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white\">\n                  <option>Alle Marken</option>\n                  <option>Mercedes</option>\n                  <option>BMW</option>\n                  <option>Audi</option>\n                  <option>Volkswagen</option>\n                  <option>Porsche</option>\n                  <option>Ford</option>\n                  <option>Opel</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-semibold text-gray-700 mb-3\">Modell</label>\n                <select className=\"w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white\">\n                  <option>Alle Modelle</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-semibold text-gray-700 mb-3\">Karosserie</label>\n                <select className=\"w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white\">\n                  <option>Alle Typen</option>\n                  <option>Limousine</option>\n                  <option>Kombi</option>\n                  <option>SUV</option>\n                  <option>Cabrio</option>\n                  <option>Coupé</option>\n                  <option>Kleinwagen</option>\n                </select>\n              </div>\n              <div className=\"flex items-end\">\n                <Button size=\"xl\" className=\"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200\" asChild>\n                  <Link href=\"/fahrzeuge\">\n                    <Search className=\"mr-2 h-5 w-5\" />\n                    Suchen\n                  </Link>\n                </Button>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center\"\n          >\n            <Button size=\"xl\" variant=\"outline\" className=\"border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm font-semibold\" asChild>\n              <Link href=\"/fahrzeuge\">Alle Fahrzeuge ansehen</Link>\n            </Button>\n            <Button size=\"xl\" variant=\"outline\" className=\"border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm font-semibold\" asChild>\n              <Link href=\"/kontakt\">\n                <Phone className=\"mr-2 h-5 w-5\" />\n                0461-66353453\n              </Link>\n            </Button>\n          </motion.div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 1, delay: 1 }}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\"\n        >\n          <div className=\"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce\"></div>\n          </div>\n        </motion.div>\n      </section>\n\n      {/* Services Section */}\n      <section className=\"py-20 bg-gradient-to-br from-gray-50 to-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n              Unsere <span className=\"bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent\">Dienstleistungen</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Wir bieten Ihnen umfassende Services rund um Ihr Fahrzeug - von der Beratung bis zum Verkauf.\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-3 gap-8 lg:gap-12\">\n            {/* Fahrzeugverkauf */}\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100\"\n            >\n              <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <ShoppingCart className=\"h-10 w-10 text-white\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors\">Fahrzeugverkauf</h3>\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  Große Auswahl an hochwertigen Gebrauchtwagen aller Marken und Modelle.\n                </p>\n                <ul className=\"text-gray-600 space-y-3 mb-8\">\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Geprüfte Fahrzeugqualität\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Faire Preise\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Umfassende Beratung\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Garantieleistungen\n                  </li>\n                </ul>\n                <Button variant=\"outline\" className=\"w-full border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-semibold group-hover:shadow-lg transition-all duration-300\">\n                  Mehr erfahren\n                  <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </div>\n            </motion.div>\n\n            {/* Fahrzeugankauf */}\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100\"\n            >\n              <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <DollarSign className=\"h-10 w-10 text-white\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors\">Fahrzeugankauf</h3>\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  Wir kaufen Ihr Fahrzeug zu fairen Marktpreisen - schnell und unkompliziert.\n                </p>\n                <ul className=\"text-gray-600 space-y-3 mb-8\">\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Kostenlose Bewertung\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Sofortige Barauszahlung\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Alle Marken und Modelle\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Professionelle Abwicklung\n                  </li>\n                </ul>\n                <Button variant=\"outline\" className=\"w-full border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-semibold group-hover:shadow-lg transition-all duration-300\">\n                  Mehr erfahren\n                  <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </div>\n            </motion.div>\n\n            {/* Beratung & Service */}\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100\"\n            >\n              <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <Headphones className=\"h-10 w-10 text-white\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors\">Beratung & Service</h3>\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  Persönliche Beratung und umfassender Service für alle Ihre Fahrzeugbedürfnisse.\n                </p>\n                <ul className=\"text-gray-600 space-y-3 mb-8\">\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Individuelle Beratung\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Finanzierungshilfe\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Zulassungsservice\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"h-5 w-5 text-green-500 mr-3 flex-shrink-0\" />\n                    Nachbetreuung\n                  </li>\n                </ul>\n                <Button variant=\"outline\" className=\"w-full border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-semibold group-hover:shadow-lg transition-all duration-300\">\n                  Mehr erfahren\n                  <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Über uns / Willkommen Section */}\n      <section className=\"relative py-20 bg-white overflow-hidden\">\n        {/* Background Pattern */}\n        <div className=\"absolute inset-0 opacity-5\">\n          <div className=\"absolute inset-0\" style={{\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n          }}></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n                Herzlich <span className=\"bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent\">Willkommen</span>\n              </h2>\n              <p className=\"text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n                Kundenzufriedenheit hat für uns höchste Priorität.\n              </p>\n              <p className=\"text-xl lg:text-2xl font-semibold text-gray-800 mt-4\">\n                Nicht nur dies, sondern <span className=\"text-orange-500\">WIR wollen DICH begeistern!</span>\n              </p>\n            </motion.div>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16\">\n            {/* Left Side - Features */}\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"space-y-8\"\n            >\n              <div className=\"grid sm:grid-cols-2 gap-6\">\n                <div className=\"group\">\n                  <div className=\"flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <CheckCircle className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"font-bold text-gray-900\">Präzise Antworten</p>\n                      <p className=\"text-sm text-gray-600\">Ehrliche Beratung</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"group\">\n                  <div className=\"flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <Users className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"font-bold text-gray-900\">Wie zu Hause</p>\n                      <p className=\"text-sm text-gray-600\">Familiäre Atmosphäre</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"group\">\n                  <div className=\"flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <Star className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"font-bold text-gray-900\">Alle Freiheiten</p>\n                      <p className=\"text-sm text-gray-600\">Flexible Lösungen</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"group\">\n                  <div className=\"flex items-center space-x-4 p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <Car className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"font-bold text-gray-900\">Immer mobil</p>\n                      <p className=\"text-sm text-gray-600\">Zuverlässiger Service</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n                viewport={{ once: true }}\n                className=\"text-center lg:text-left\"\n              >\n                <p className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  Bei uns erwirbst du <span className=\"text-orange-500\">„das Auto\"</span>\n                </p>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Seit über 15 Jahren stehen wir für Qualität, Vertrauen und erstklassigen Service.\n                  Unser erfahrenes Team begleitet Sie von der ersten Beratung bis zur Schlüsselübergabe.\n                </p>\n              </motion.div>\n            </motion.div>\n\n            {/* Right Side - Contact Card */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <div className=\"bg-gradient-to-br from-gray-900 to-gray-800 rounded-3xl p-8 lg:p-12 text-white shadow-2xl\">\n                <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full -translate-y-16 translate-x-16\"></div>\n                <div className=\"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full translate-y-12 -translate-x-12\"></div>\n\n                <div className=\"relative z-10\">\n                  <div className=\"flex items-center space-x-4 mb-8\">\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center\">\n                      <Phone className=\"h-8 w-8 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"text-2xl font-bold\">Hast Du Fragen?</h3>\n                      <p className=\"text-gray-300\">Wir sind für dich da!</p>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-6\">\n                    <div>\n                      <p className=\"text-gray-300 mb-2\">Ruf uns direkt an:</p>\n                      <a\n                        href=\"tel:+4946166353453\"\n                        className=\"text-3xl lg:text-4xl font-bold text-white hover:text-orange-400 transition-colors duration-300 block\"\n                      >\n                        0461-66353453\n                      </a>\n                    </div>\n\n                    <div className=\"border-t border-gray-700 pt-6\">\n                      <div className=\"flex items-center space-x-3 text-gray-300\">\n                        <Clock className=\"h-5 w-5 text-orange-400\" />\n                        <div>\n                          <p className=\"font-semibold text-white\">Öffnungszeiten:</p>\n                          <p className=\"text-sm\">Mo-Fr: 10:00 - 18:00</p>\n                          <p className=\"text-sm\">Sa: 10:00 - 13:00</p>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center space-x-3 text-gray-300\">\n                      <MapPin className=\"h-5 w-5 text-orange-400\" />\n                      <div>\n                        <p className=\"font-semibold text-white\">Besuche uns:</p>\n                        <p className=\"text-sm\">Grönfahrtweg 22, 24955 Harrislee</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Section */}\n      <section className=\"bg-gray-50 py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"clean-card p-6 text-center\"\n            >\n              <Car className=\"h-12 w-12 primary-blue mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Wir kaufen Jedes Auto</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Sie wollen Ihr Auto verkaufen? Das trifft sich gut. In uns haben Sie einen zuverlässigen Partner.\n              </p>\n              <ul className=\"text-sm text-gray-600 space-y-2\">\n                <li>• Eine marktgerechte Schätzung Ihres Fahrzeugs</li>\n                <li>• Finanzkräftige Vormerkkunden</li>\n                <li>• Individuelle und persönliche Betreuung</li>\n                <li>• Kraftfahrzeuge jeglicher Art</li>\n              </ul>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"clean-card p-6 text-center\"\n            >\n              <Settings className=\"h-12 w-12 primary-blue mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Dienstleistungen</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Gebrauchtwagen An- und Verkauf mit umfassendem Service.\n              </p>\n              <ul className=\"text-sm text-gray-600 space-y-2\">\n                <li>• Große Auswahl an Fahrzeugen</li>\n                <li>• Wir besorgen Ihr Wunschfahrzeug</li>\n                <li>• Inzahlungnahme</li>\n                <li>• Sofortankauf gegen Bargeld</li>\n              </ul>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"clean-card p-6 text-center\"\n            >\n              <Users className=\"h-12 w-12 primary-blue mx-auto mb-4\" />\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Unser Team</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Unser Team steht Ihnen während der Öffnungszeiten zur Verfügung.\n              </p>\n              <div className=\"text-sm text-gray-600 space-y-2\">\n                <p><strong>Öffnungszeiten:</strong></p>\n                <p>Mo-Fr: 10:00 - 18:00</p>\n                <p>Sa: 10:00 - 13:00</p>\n                <p>So: Geschlossen</p>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"clean-section py-16\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900\">\n              Bereit für Ihr neues Auto?\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Kontaktieren Sie uns noch heute und finden Sie Ihr Traumfahrzeug.\n              Unser Team berät Sie gerne persönlich.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" className=\"bg-primary-blue hover:bg-primary-blue text-white px-8 py-3\" asChild>\n                <Link href=\"/kontakt\">\n                  <Phone className=\"mr-2 h-5 w-5\" />\n                  Jetzt Kontakt aufnehmen\n                </Link>\n              </Button>\n\n              <Button size=\"lg\" variant=\"outline\" className=\"border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white px-8 py-3\" asChild>\n                <Link href=\"/fahrzeuge\">\n                  <Car className=\"mr-2 h-5 w-5\" />\n                  Fahrzeuge ansehen\n                </Link>\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"relative py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full -translate-x-48 -translate-y-48\"></div>\n          <div className=\"absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full translate-x-48 translate-y-48\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl lg:text-5xl font-bold mb-6\">\n              Zahlen, die für sich <span className=\"bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent\">sprechen</span>\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              Über 15 Jahre Erfahrung und tausende zufriedene Kunden sprechen für unsere Qualität und Zuverlässigkeit.\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Fahrzeuge im Bestand */}\n            <motion.div\n              initial={{ opacity: 0, y: 50, scale: 0.8 }}\n              whileInView={{ opacity: 1, y: 0, scale: 1 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"group text-center\"\n            >\n              <div className=\"relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    <Car className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300\">\n                    5k+\n                  </div>\n                  <div className=\"text-gray-300 font-semibold text-lg\">Fahrzeuge</div>\n                  <div className=\"text-gray-400 text-sm\">im Bestand</div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Probefahrten */}\n            <motion.div\n              initial={{ opacity: 0, y: 50, scale: 0.8 }}\n              whileInView={{ opacity: 1, y: 0, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"group text-center\"\n            >\n              <div className=\"relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    <Calendar className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300\">\n                    680+\n                  </div>\n                  <div className=\"text-gray-300 font-semibold text-lg\">Probefahrten</div>\n                  <div className=\"text-gray-400 text-sm\">durchgeführt</div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Jahre Erfahrung */}\n            <motion.div\n              initial={{ opacity: 0, y: 50, scale: 0.8 }}\n              whileInView={{ opacity: 1, y: 0, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"group text-center\"\n            >\n              <div className=\"relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    <Award className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300\">\n                    15+\n                  </div>\n                  <div className=\"text-gray-300 font-semibold text-lg\">Jahre</div>\n                  <div className=\"text-gray-400 text-sm\">Erfahrung</div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Kundenzufriedenheit */}\n            <motion.div\n              initial={{ opacity: 0, y: 50, scale: 0.8 }}\n              whileInView={{ opacity: 1, y: 0, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n              className=\"group text-center\"\n            >\n              <div className=\"relative bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-orange-500/30 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/20\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    <Star className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"text-5xl lg:text-6xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 group-hover:scale-105 transition-transform duration-300\">\n                    100%\n                  </div>\n                  <div className=\"text-gray-300 font-semibold text-lg\">Kunden-</div>\n                  <div className=\"text-gray-400 text-sm\">zufriedenheit</div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Trust Indicators */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"mt-16 text-center\"\n          >\n            <p className=\"text-gray-300 text-lg mb-8\">\n              Vertrauen Sie auf unsere Expertise und werden Sie Teil unserer zufriedenen Kundenfamilie\n            </p>\n            <div className=\"flex flex-wrap justify-center items-center gap-8 opacity-60\">\n              <div className=\"flex items-center space-x-2\">\n                <Shield className=\"h-6 w-6 text-green-400\" />\n                <span className=\"text-gray-300\">Geprüfte Qualität</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <CheckCircle className=\"h-6 w-6 text-green-400\" />\n                <span className=\"text-gray-300\">Faire Preise</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Users className=\"h-6 w-6 text-green-400\" />\n                <span className=\"text-gray-300\">Persönlicher Service</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Final CTA Section */}\n      <section className=\"bg-orange-500 text-white section-padding\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <h2 className=\"text-4xl lg:text-5xl font-bold\">\n              Ihr Traumauto ist nur einen Besuch entfernt!\n            </h2>\n            <p className=\"text-xl opacity-90\">\n              Besuchen Sie uns noch heute für nur <strong>149€/Monat</strong>\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" className=\"bg-white text-orange-500 hover:bg-gray-100 px-8 py-3\" asChild>\n                <Link href=\"/kontakt\">\n                  Besuchen Sie uns heute\n                </Link>\n              </Button>\n\n              <Button size=\"lg\" variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-orange-500 px-8 py-3\" asChild>\n                <Link href=\"/fahrzeuge\">\n                  <Phone className=\"mr-2 h-5 w-5\" />\n                  0461-66353453\n                </Link>\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,WAAU;;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;;kDAE3C,8OAAC;wCAAG,WAAU;;4CAA2E;4CAC5E;0DACX,8OAAC;gDAAK,WAAU;0DAA4E;;;;;;;;;;;;kDAI9F,8OAAC;wCAAE,WAAU;kDAAuF;;;;;;;;;;;;0CAMtG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;oCAAK,MAAM;gCAAU;gCACvD,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAClE,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAClE,8OAAC;oDAAO,WAAU;8DAChB,cAAA,8OAAC;kEAAO;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAClE,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;sEACR,8OAAC;sEAAO;;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;gDAA2K,OAAO;0DAC5M,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAU,WAAU;wCAAuF,OAAO;kDAC1I,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAa;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAU,WAAU;wCAAuF,OAAO;kDAC1I,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAE;wBACpC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;;wCAAoD;sDACzD,8OAAC;4CAAK,WAAU;sDAA4E;;;;;;;;;;;;8CAErG,8OAAC;oCAAE,WAAU;8CAA0D;;;;;;;;;;;;sCAKzE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC;oDAAG,WAAU;8DAAsF;;;;;;8DACpG,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;;;;;;;8DAIzE,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,WAAU;;wDAAyJ;sEAE3L,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC;oDAAG,WAAU;8DAAsF;;;;;;8DACpG,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;;;;;;;8DAIzE,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,WAAU;;wDAAyJ;sEAE3L,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC;oDAAG,WAAU;8DAAsF;;;;;;8DACpG,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;;;;;;;8DAIzE,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,WAAU;;wDAAyJ;sEAE3L,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAmB,OAAO;gCACvC,iBAAiB,CAAC,gQAAgQ,CAAC;4BACrR;;;;;;;;;;;kCAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC;4CAAG,WAAU;;gDAAoD;8DACvD,8OAAC;oDAAK,WAAU;8DAA4E;;;;;;;;;;;;sDAEvG,8OAAC;4CAAE,WAAU;sDAAsE;;;;;;sDAGnF,8OAAC;4CAAE,WAAU;;gDAAuD;8DAC1C,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;0CAKhE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;8EAEzB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAA0B;;;;;;sFACvC,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;kEAK3C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAA0B;;;;;;sFACvC,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;kEAK3C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAA0B;;;;;;sFACvC,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;kEAK3C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAA0B;;;;;;sFACvC,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,8OAAC;wDAAE,WAAU;;4DAAwC;0EAC/B,8OAAC;gEAAK,WAAU;0EAAkB;;;;;;;;;;;;kEAExD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAQjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAqB;;;;;;sFACnC,8OAAC;4EAAE,WAAU;sFAAgB;;;;;;;;;;;;;;;;;;sEAIjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAqB;;;;;;sFAClC,8OAAC;4EACC,MAAK;4EACL,WAAU;sFACX;;;;;;;;;;;;8EAKH,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGAA2B;;;;;;kGACxC,8OAAC;wFAAE,WAAU;kGAAU;;;;;;kGACvB,8OAAC;wFAAE,WAAU;kGAAU;;;;;;;;;;;;;;;;;;;;;;;8EAK7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;;8FACC,8OAAC;oFAAE,WAAU;8FAA2B;;;;;;8FACxC,8OAAC;oFAAE,WAAU;8FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYzC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAE,cAAA,8OAAC;8DAAO;;;;;;;;;;;0DACX,8OAAC;0DAAE;;;;;;0DACH,8OAAC;0DAAE;;;;;;0DACH,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;wCAA6D,OAAO;kDAC9F,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAKtC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAU,WAAU;wCAAyF,OAAO;kDAC5I,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;;4CAAsC;0DAC7B,8OAAC;gDAAK,WAAU;0DAA4E;;;;;;;;;;;;kDAEnH,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAKzD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAI;wCACzC,aAAa;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCAC1C,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,8OAAC;4DAAI,WAAU;sEAAwK;;;;;;sEAGvL,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAI;wCACzC,aAAa;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCAC1C,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;4DAAI,WAAU;sEAAwK;;;;;;sEAGvL,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAI;wCACzC,aAAa;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCAC1C,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;sEAAwK;;;;;;sEAGvL,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAI;wCACzC,aAAa;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCAC1C,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DAAI,WAAU;sEAAwK;;;;;;sEAGvL,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAG/C,8OAAC;gCAAE,WAAU;;oCAAqB;kDACI,8OAAC;kDAAO;;;;;;;;;;;;0CAG9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;wCAAuD,OAAO;kDACxF,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAW;;;;;;;;;;;kDAKxB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAU,WAAU;wCAAyE,OAAO;kDAC5H,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpD", "debugId": null}}]}