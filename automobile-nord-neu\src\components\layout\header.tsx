'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Menu, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigation = [
    { name: 'Fahrzeuge im Lager', href: '/fahrzeuge' },
    { name: 'Verkaufe dein Auto', href: '/verkaufen' },
    { name: 'Über uns', href: '/ueber-uns' },
    { name: 'Service', href: '/service' },
    { name: '<PERSON>aran<PERSON>', href: '/garantie' },
    { name: 'Konta<PERSON>', href: '/kontakt' },
  ];

  return (
    <header className="bg-white border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <div className="text-2xl font-bold text-gray-900">
              <span className="text-black">Automobile</span>{' '}
              <span className="text-black font-light">Nord</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Right Side - Language & CTA */}
          <div className="hidden lg:flex items-center space-x-6">
            {/* Language Selector */}
            <div className="flex items-center space-x-1 text-sm text-gray-700 cursor-pointer hover:text-black transition-colors">
              <span>DE</span>
              <ChevronDown className="h-4 w-4" />
            </div>

            {/* CTA Button */}
            <Button
              variant="default"
              size="default"
              className="bg-black text-white hover:bg-gray-800 px-6 py-2 rounded-md font-medium"
              asChild
            >
              <Link href="/verkaufen">
                Verkaufe dein Auto
              </Link>
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
              className="text-gray-700"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="lg:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-100">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-700 hover:text-black block px-3 py-3 text-base font-medium transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
            <div className="pt-4 border-t border-gray-100">
              <Button
                variant="default"
                size="lg"
                className="w-full bg-black text-white hover:bg-gray-800"
                asChild
              >
                <Link href="/verkaufen" onClick={() => setIsMenuOpen(false)}>
                  Verkaufe dein Auto
                </Link>
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
