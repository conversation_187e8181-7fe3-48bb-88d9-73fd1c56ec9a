{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('de-DE', {\n    style: 'currency',\n    currency: 'EUR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(price);\n}\n\nexport function formatKilometers(km: number): string {\n  return new Intl.NumberFormat('de-DE').format(km) + ' km';\n}\n\nexport function formatYear(date: string): string {\n  return new Date(date).getFullYear().toString();\n}\n\nexport function formatPower(kw: number): string {\n  const ps = Math.round(kw * 1.36);\n  return `${kw} kW / ${ps} PS`;\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,EAAU;IACzC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC,MAAM;AACrD;AAEO,SAAS,WAAW,IAAY;IACrC,OAAO,IAAI,KAAK,MAAM,WAAW,GAAG,QAAQ;AAC9C;AAEO,SAAS,YAAY,EAAU;IACpC,MAAM,KAAK,KAAK,KAAK,CAAC,KAAK;IAC3B,OAAO,GAAG,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC;AAC9B;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary-600 text-white shadow-md hover:bg-primary-700 hover:shadow-lg active:scale-95\",\n        destructive:\n          \"bg-accent-600 text-white shadow-md hover:bg-accent-700 hover:shadow-lg active:scale-95\",\n        outline:\n          \"border border-secondary-300 bg-white text-secondary-900 shadow-sm hover:bg-secondary-50 hover:border-secondary-400 active:scale-95\",\n        secondary:\n          \"bg-secondary-100 text-secondary-900 shadow-sm hover:bg-secondary-200 active:scale-95\",\n        ghost:\n          \"text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900 active:scale-95\",\n        link:\n          \"text-primary-600 underline-offset-4 hover:underline hover:text-primary-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 px-4 py-1.5 text-xs\",\n        lg: \"h-12 px-8 py-3\",\n        xl: \"h-14 px-10 py-4 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    if (asChild) {\n      return (\n        <Slot\n          className={cn(buttonVariants({ variant, size, className }))}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </Slot>\n      );\n    }\n\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,oRACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,IAAI,SAAS;QACX,qBACE,8OAAC,gKAAA,CAAA,OAAI;YACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;gBAAE;gBAAS;gBAAM;YAAU;YACxD,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, ChevronDown } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Fahrzeuge im Lager', href: '/fahrzeuge' },\n    { name: 'Verkaufe dein Auto', href: '/verkaufen' },\n    { name: 'Über uns', href: '/ueber-uns' },\n    { name: 'Service', href: '/service' },\n    { name: '<PERSON><PERSON><PERSON>', href: '/garantie' },\n    { name: 'Konta<PERSON>', href: '/kontakt' },\n  ];\n\n  return (\n    <header className=\"bg-white border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24\">\n        <div className=\"flex justify-between items-center h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-gray-900\">\n              <span className=\"text-black\">Automobile</span>{' '}\n              <span className=\"text-black font-light\">Nord</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Right Side - Language & CTA */}\n          <div className=\"hidden lg:flex items-center space-x-6\">\n            {/* Language Selector */}\n            <div className=\"flex items-center space-x-1 text-sm text-gray-700 cursor-pointer hover:text-black transition-colors\">\n              <span>DE</span>\n              <ChevronDown className=\"h-4 w-4\" />\n            </div>\n\n            {/* CTA Button */}\n            <Button\n              variant=\"default\"\n              size=\"default\"\n              className=\"bg-black text-white hover:bg-gray-800 px-6 py-2 rounded-md font-medium\"\n              asChild\n            >\n              <Link href=\"/verkaufen\">\n                Verkaufe dein Auto\n              </Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"lg:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              aria-label=\"Toggle menu\"\n              className=\"text-gray-700\"\n            >\n              {isMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isMenuOpen && (\n        <div className=\"lg:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-100\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-black block px-3 py-3 text-base font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n            <div className=\"pt-4 border-t border-gray-100\">\n              <Button\n                variant=\"default\"\n                size=\"lg\"\n                className=\"w-full bg-black text-white hover:bg-gray-800\"\n                asChild\n              >\n                <Link href=\"/verkaufen\" onClick={() => setIsMenuOpen(false)}>\n                  Verkaufe dein Auto\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAY,MAAM;QAAa;QACvC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAa;;;;;;oCAAkB;kDAC/C,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAIzB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAa;;;;;;;;;;;;;;;;;sCAO5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc,CAAC;gCAC9B,cAAW;gCACX,WAAU;0CAET,2BACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;6FAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,IAAI;+BALL,KAAK,IAAI;;;;;sCAQlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,SAAS,IAAM,cAAc;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7E;uCAEe", "debugId": null}}]}