import React from 'react';
import Link from 'next/link';
import { ArrowUp, Facebook, Instagram, Youtube, Linkedin, Phone, MapPin, Mail, Clock } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-100">
      {/* Back to top button */}
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24 py-8">
        <div className="text-center">
          <a
            href="#top"
            className="inline-flex items-center justify-center w-12 h-12 bg-black text-white rounded-full hover:bg-gray-800 transition-colors"
          >
            <ArrowUp className="h-5 w-5" />
          </a>
        </div>
      </div>

      {/* Main footer content */}
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">

          {/* Left Column - Navigation */}
          <div className="space-y-6">
            <nav>
              <ul className="space-y-3">
                <li>
                  <Link href="/" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Home
                  </Link>
                </li>
                <li>
                  <Link href="/fahrzeuge" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Fahrzeuge im Lager
                  </Link>
                </li>
                <li>
                  <Link href="/verkaufen" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Verkaufe dein Auto
                  </Link>
                </li>
                <li>
                  <Link href="/ueber-uns" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Über uns
                  </Link>
                </li>
              </ul>
            </nav>
          </div>

          {/* Center Column - Logo */}
          <div className="text-center">
            <Link href="/" className="inline-block">
              <div className="text-3xl font-bold text-gray-900">
                <span className="text-black">Automobile</span>{' '}
                <span className="text-black font-light">Nord</span>
              </div>
            </Link>
          </div>

          {/* Right Column - Navigation & Social */}
          <div className="space-y-6">
            <nav>
              <ul className="space-y-3 lg:text-right">
                <li>
                  <Link href="/service" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Service
                  </Link>
                </li>
                <li>
                  <Link href="/garantie" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Garantie
                  </Link>
                </li>
                <li>
                  <Link href="/karriere" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Karriere
                  </Link>
                </li>
                <li>
                  <Link href="/kontakt" className="text-gray-700 hover:text-black transition-colors font-medium">
                    Kontakt
                  </Link>
                </li>
              </ul>
            </nav>

            {/* Social Media */}
            <div className="flex space-x-4 lg:justify-end">
              <a
                href="https://www.instagram.com/automobilenord"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="https://www.youtube.com/channel/UCVIvQHOig3Vw9sxNj_tUBcQ"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                <Youtube className="h-5 w-5" />
              </a>
              <a
                href="https://www.facebook.com/automobilenord"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="https://www.linkedin.com/company/automobile-nord"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="bg-gray-50 border-t border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

            {/* Contact Details */}
            <div className="lg:col-span-2">
              <div className="space-y-4">
                <div>
                  <p className="font-bold text-gray-900">Telefon</p>
                  <a
                    href="tel:+4946166353453"
                    className="text-gray-700 hover:text-black transition-colors"
                  >
                    +49 461 66353453
                  </a>
                </div>

                <div>
                  <p className="font-bold text-gray-900">Adresse</p>
                  <p className="text-gray-700">Grönfahrtweg 22, 24955 Harrislee</p>
                  <a
                    href="https://maps.google.com/?q=Grönfahrtweg+22,+24955+Harrislee"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-700 hover:text-black transition-colors underline"
                  >
                    Auf Karte anzeigen
                  </a>
                </div>

                <div>
                  <p className="font-bold text-gray-900">E-Mail</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-gray-700 hover:text-black transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>

            {/* Opening Hours */}
            <div>
              <p className="font-bold text-gray-900 mb-4">Öffnungszeiten</p>
              <div className="space-y-1 text-gray-700">
                <p>Mo-Fr: 10:00-18:00</p>
                <p>Sa: 10:00-13:00</p>
                <p>So: Geschlossen</p>
              </div>
            </div>

            {/* Language Selector */}
            <div className="flex justify-start lg:justify-end">
              <div className="flex items-center space-x-2 text-gray-700">
                <span className="font-medium">DE</span>
                <span className="text-gray-400">|</span>
                <span className="text-gray-400">EN</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Copyright Section */}
      <div className="bg-white border-t border-gray-100">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 xl:px-16 2xl:px-24 py-6">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">

            {/* Copyright */}
            <div className="text-center lg:text-left">
              <p className="font-bold text-gray-900">
                © Copyright {currentYear} Automobile Nord GmbH
              </p>
              <p className="text-gray-600 text-sm">
                Org.nr. DE123456789 USt-IdNr. DE123456789
              </p>
            </div>

            {/* Legal Links & Powered by */}
            <div className="text-center lg:text-right space-y-2">
              <div className="text-gray-600 text-sm">
                <span>Diese Seite ist geschützt durch reCAPTCHA und die Google </span>
                <Link href="/datenschutz" className="hover:text-black transition-colors">
                  Datenschutzerklärung
                </Link>
                <span> und </span>
                <Link href="/agb" className="hover:text-black transition-colors">
                  Nutzungsbedingungen
                </Link>
                <span> gelten. </span>
                <Link href="/impressum" className="hover:text-black transition-colors">
                  Impressum
                </Link>
              </div>
              <div className="text-gray-500 text-sm">
                <Link href="https://augment.dev" className="hover:text-black transition-colors">
                  Powered by Augment
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
